{"name": "tauri-plugin-macos-permissions-api", "version": "2.3.0", "author": "ayangweb", "description": "Support for checking and requesting macos system permissions.", "keywords": ["tauri", "tauri-plugin", "macos-permissions", "accessibility", "full-disk-access", "screen-recording", "microphone", "camera", "input-monitoring"], "repository": {"type": "git", "url": "git+https://github.com/ayangweb/tauri-plugin-macos-permissions.git"}, "homepage": "https://github.com/ayangweb/tauri-plugin-macos-permissions#readme", "bugs": "https://github.com/ayangweb/tauri-plugin-macos-permissions/issues", "license": "MIT", "type": "module", "types": "./dist-js/index.d.ts", "main": "./dist-js/index.cjs", "module": "./dist-js/index.js", "exports": {"types": "./dist-js/index.d.ts", "import": "./dist-js/index.js", "require": "./dist-js/index.cjs"}, "files": ["dist-js", "README.md"], "scripts": {"build": "rollup -c", "prepublishOnly": "pnpm build", "pretest": "pnpm build", "release": "release-it"}, "dependencies": {"@tauri-apps/api": "^2.5.0"}, "devDependencies": {"@rollup/plugin-typescript": "^11.1.6", "release-it": "^17.11.0", "rollup": "^4.40.0", "tslib": "^2.8.1", "typescript": "^5.8.3"}}