## Default Permission

Default permissions for the plugin

#### This default permission set includes the following:

- `allow-check-accessibility-permission`
- `allow-request-accessibility-permission`
- `allow-check-full-disk-access-permission`
- `allow-request-full-disk-access-permission`
- `allow-check-screen-recording-permission`
- `allow-request-screen-recording-permission`
- `allow-check-microphone-permission`
- `allow-request-microphone-permission`
- `allow-check-camera-permission`
- `allow-request-camera-permission`
- `allow-check-input-monitoring-permission`
- `allow-request-input-monitoring-permission`
- `allow-check-photokit-permission`
- `allow-request-photokit-permission`
- `allow-register-photokit-permission-listener`
- `allow-unregister-photokit-permission-listener`
- `allow-get-photokit-permission-listeners`

## Permission Table

<table>
<tr>
<th>Identifier</th>
<th>Description</th>
</tr>


<tr>
<td>

`macos-permissions:allow-check-accessibility-permission`

</td>
<td>

Enables the check_accessibility_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:deny-check-accessibility-permission`

</td>
<td>

Denies the check_accessibility_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:allow-check-camera-permission`

</td>
<td>

Enables the check_camera_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:deny-check-camera-permission`

</td>
<td>

Denies the check_camera_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:allow-check-full-disk-access-permission`

</td>
<td>

Enables the check_full_disk_access_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:deny-check-full-disk-access-permission`

</td>
<td>

Denies the check_full_disk_access_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:allow-check-input-monitoring-permission`

</td>
<td>

Enables the check_input_monitoring_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:deny-check-input-monitoring-permission`

</td>
<td>

Denies the check_input_monitoring_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:allow-check-microphone-permission`

</td>
<td>

Enables the check_microphone_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:deny-check-microphone-permission`

</td>
<td>

Denies the check_microphone_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:allow-check-photokit-permission`

</td>
<td>

Enables the check_photokit_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:deny-check-photokit-permission`

</td>
<td>

Denies the check_photokit_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:allow-check-screen-recording-permission`

</td>
<td>

Enables the check_screen_recording_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:deny-check-screen-recording-permission`

</td>
<td>

Denies the check_screen_recording_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:allow-get-photokit-permission-listeners`

</td>
<td>

Enables the get_photokit_permission_listeners command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:deny-get-photokit-permission-listeners`

</td>
<td>

Denies the get_photokit_permission_listeners command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:allow-register-photokit-permission-listener`

</td>
<td>

Enables the register_photokit_permission_listener command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:deny-register-photokit-permission-listener`

</td>
<td>

Denies the register_photokit_permission_listener command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:allow-request-accessibility-permission`

</td>
<td>

Enables the request_accessibility_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:deny-request-accessibility-permission`

</td>
<td>

Denies the request_accessibility_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:allow-request-camera-permission`

</td>
<td>

Enables the request_camera_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:deny-request-camera-permission`

</td>
<td>

Denies the request_camera_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:allow-request-full-disk-access-permission`

</td>
<td>

Enables the request_full_disk_access_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:deny-request-full-disk-access-permission`

</td>
<td>

Denies the request_full_disk_access_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:allow-request-input-monitoring-permission`

</td>
<td>

Enables the request_input_monitoring_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:deny-request-input-monitoring-permission`

</td>
<td>

Denies the request_input_monitoring_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:allow-request-microphone-permission`

</td>
<td>

Enables the request_microphone_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:deny-request-microphone-permission`

</td>
<td>

Denies the request_microphone_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:allow-request-photokit-permission`

</td>
<td>

Enables the request_photokit_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:deny-request-photokit-permission`

</td>
<td>

Denies the request_photokit_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:allow-request-screen-recording-permission`

</td>
<td>

Enables the request_screen_recording_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:deny-request-screen-recording-permission`

</td>
<td>

Denies the request_screen_recording_permission command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:allow-unregister-photokit-permission-listener`

</td>
<td>

Enables the unregister_photokit_permission_listener command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`macos-permissions:deny-unregister-photokit-permission-listener`

</td>
<td>

Denies the unregister_photokit_permission_listener command without any pre-configured scope.

</td>
</tr>
</table>
