# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-request-screen-recording-permission"
description = "Enables the request_screen_recording_permission command without any pre-configured scope."
commands.allow = ["request_screen_recording_permission"]

[[permission]]
identifier = "deny-request-screen-recording-permission"
description = "Denies the request_screen_recording_permission command without any pre-configured scope."
commands.deny = ["request_screen_recording_permission"]
