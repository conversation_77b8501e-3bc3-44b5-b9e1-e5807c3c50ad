# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-request-full-disk-access-permission"
description = "Enables the request_full_disk_access_permission command without any pre-configured scope."
commands.allow = ["request_full_disk_access_permission"]

[[permission]]
identifier = "deny-request-full-disk-access-permission"
description = "Denies the request_full_disk_access_permission command without any pre-configured scope."
commands.deny = ["request_full_disk_access_permission"]
