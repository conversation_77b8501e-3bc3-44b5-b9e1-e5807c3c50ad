# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-check-screen-recording-permission"
description = "Enables the check_screen_recording_permission command without any pre-configured scope."
commands.allow = ["check_screen_recording_permission"]

[[permission]]
identifier = "deny-check-screen-recording-permission"
description = "Denies the check_screen_recording_permission command without any pre-configured scope."
commands.deny = ["check_screen_recording_permission"]
