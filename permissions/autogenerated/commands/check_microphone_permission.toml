# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-check-microphone-permission"
description = "Enables the check_microphone_permission command without any pre-configured scope."
commands.allow = ["check_microphone_permission"]

[[permission]]
identifier = "deny-check-microphone-permission"
description = "Denies the check_microphone_permission command without any pre-configured scope."
commands.deny = ["check_microphone_permission"]
