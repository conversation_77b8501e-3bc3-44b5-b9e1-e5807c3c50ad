# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-request-camera-permission"
description = "Enables the request_camera_permission command without any pre-configured scope."
commands.allow = ["request_camera_permission"]

[[permission]]
identifier = "deny-request-camera-permission"
description = "Denies the request_camera_permission command without any pre-configured scope."
commands.deny = ["request_camera_permission"]
