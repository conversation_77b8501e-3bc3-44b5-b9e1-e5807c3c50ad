# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-check-full-disk-access-permission"
description = "Enables the check_full_disk_access_permission command without any pre-configured scope."
commands.allow = ["check_full_disk_access_permission"]

[[permission]]
identifier = "deny-check-full-disk-access-permission"
description = "Denies the check_full_disk_access_permission command without any pre-configured scope."
commands.deny = ["check_full_disk_access_permission"]
