# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-request-microphone-permission"
description = "Enables the request_microphone_permission command without any pre-configured scope."
commands.allow = ["request_microphone_permission"]

[[permission]]
identifier = "deny-request-microphone-permission"
description = "Denies the request_microphone_permission command without any pre-configured scope."
commands.deny = ["request_microphone_permission"]
