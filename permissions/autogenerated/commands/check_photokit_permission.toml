# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-check-photokit-permission"
description = "Enables the check_photokit_permission command without any pre-configured scope."
commands.allow = ["check_photokit_permission"]

[[permission]]
identifier = "deny-check-photokit-permission"
description = "Denies the check_photokit_permission command without any pre-configured scope."
commands.deny = ["check_photokit_permission"]
