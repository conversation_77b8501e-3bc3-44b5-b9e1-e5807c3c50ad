{"$schema": "http://json-schema.org/draft-07/schema#", "title": "PermissionFile", "description": "Permission file that can define a default permission, a set of permissions or a list of inlined permissions.", "type": "object", "properties": {"default": {"description": "The default permission set for the plugin", "anyOf": [{"$ref": "#/definitions/DefaultPermission"}, {"type": "null"}]}, "set": {"description": "A list of permissions sets defined", "type": "array", "items": {"$ref": "#/definitions/PermissionSet"}}, "permission": {"description": "A list of inlined permissions", "default": [], "type": "array", "items": {"$ref": "#/definitions/Permission"}}}, "definitions": {"DefaultPermission": {"description": "The default permission set of the plugin.\n\nWorks similarly to a permission with the \"default\" identifier.", "type": "object", "required": ["permissions"], "properties": {"version": {"description": "The version of the permission.", "type": ["integer", "null"], "format": "uint64", "minimum": 1.0}, "description": {"description": "Human-readable description of what the permission does. Tauri convention is to use `<h4>` headings in markdown content for Tauri documentation generation purposes.", "type": ["string", "null"]}, "permissions": {"description": "All permissions this set contains.", "type": "array", "items": {"type": "string"}}}}, "PermissionSet": {"description": "A set of direct permissions grouped together under a new name.", "type": "object", "required": ["description", "identifier", "permissions"], "properties": {"identifier": {"description": "A unique identifier for the permission.", "type": "string"}, "description": {"description": "Human-readable description of what the permission does.", "type": "string"}, "permissions": {"description": "All permissions this set contains.", "type": "array", "items": {"$ref": "#/definitions/PermissionKind"}}}}, "Permission": {"description": "Descriptions of explicit privileges of commands.\n\nIt can enable commands to be accessible in the frontend of the application.\n\nIf the scope is defined it can be used to fine grain control the access of individual or multiple commands.", "type": "object", "required": ["identifier"], "properties": {"version": {"description": "The version of the permission.", "type": ["integer", "null"], "format": "uint64", "minimum": 1.0}, "identifier": {"description": "A unique identifier for the permission.", "type": "string"}, "description": {"description": "Human-readable description of what the permission does. Tauri internal convention is to use `<h4>` headings in markdown content for Tauri documentation generation purposes.", "type": ["string", "null"]}, "commands": {"description": "Allowed or denied commands when using this permission.", "default": {"allow": [], "deny": []}, "allOf": [{"$ref": "#/definitions/Commands"}]}, "scope": {"description": "Allowed or denied scoped when using this permission.", "allOf": [{"$ref": "#/definitions/Scopes"}]}, "platforms": {"description": "Target platforms this permission applies. By default all platforms are affected by this permission.", "type": ["array", "null"], "items": {"$ref": "#/definitions/Target"}}}}, "Commands": {"description": "Allowed and denied commands inside a permission.\n\nIf two commands clash inside of `allow` and `deny`, it should be denied by default.", "type": "object", "properties": {"allow": {"description": "Allowed command.", "default": [], "type": "array", "items": {"type": "string"}}, "deny": {"description": "Denied command, which takes priority.", "default": [], "type": "array", "items": {"type": "string"}}}}, "Scopes": {"description": "An argument for fine grained behavior control of Tauri commands.\n\nIt can be of any serde serializable type and is used to allow or prevent certain actions inside a Tauri command. The configured scope is passed to the command and will be enforced by the command implementation.\n\n## Example\n\n```json { \"allow\": [{ \"path\": \"$HOME/**\" }], \"deny\": [{ \"path\": \"$HOME/secret.txt\" }] } ```", "type": "object", "properties": {"allow": {"description": "Data that defines what is allowed by the scope.", "type": ["array", "null"], "items": {"$ref": "#/definitions/Value"}}, "deny": {"description": "Data that defines what is denied by the scope. This should be prioritized by validation logic.", "type": ["array", "null"], "items": {"$ref": "#/definitions/Value"}}}}, "Value": {"description": "All supported ACL values.", "anyOf": [{"description": "Represents a null JSON value.", "type": "null"}, {"description": "Represents a [`bool`].", "type": "boolean"}, {"description": "Represents a valid ACL [`Number`].", "allOf": [{"$ref": "#/definitions/Number"}]}, {"description": "Represents a [`String`].", "type": "string"}, {"description": "Represents a list of other [`Value`]s.", "type": "array", "items": {"$ref": "#/definitions/Value"}}, {"description": "Represents a map of [`String`] keys to [`Value`]s.", "type": "object", "additionalProperties": {"$ref": "#/definitions/Value"}}]}, "Number": {"description": "A valid ACL number.", "anyOf": [{"description": "Represents an [`i64`].", "type": "integer", "format": "int64"}, {"description": "Represents a [`f64`].", "type": "number", "format": "double"}]}, "Target": {"description": "Platform target.", "oneOf": [{"description": "MacOS.", "type": "string", "enum": ["macOS"]}, {"description": "Windows.", "type": "string", "enum": ["windows"]}, {"description": "Linux.", "type": "string", "enum": ["linux"]}, {"description": "Android.", "type": "string", "enum": ["android"]}, {"description": "iOS.", "type": "string", "enum": ["iOS"]}]}, "PermissionKind": {"type": "string", "oneOf": [{"description": "Enables the check_accessibility_permission command without any pre-configured scope.", "type": "string", "const": "allow-check-accessibility-permission", "markdownDescription": "Enables the check_accessibility_permission command without any pre-configured scope."}, {"description": "Denies the check_accessibility_permission command without any pre-configured scope.", "type": "string", "const": "deny-check-accessibility-permission", "markdownDescription": "Denies the check_accessibility_permission command without any pre-configured scope."}, {"description": "Enables the check_camera_permission command without any pre-configured scope.", "type": "string", "const": "allow-check-camera-permission", "markdownDescription": "Enables the check_camera_permission command without any pre-configured scope."}, {"description": "Denies the check_camera_permission command without any pre-configured scope.", "type": "string", "const": "deny-check-camera-permission", "markdownDescription": "Denies the check_camera_permission command without any pre-configured scope."}, {"description": "Enables the check_full_disk_access_permission command without any pre-configured scope.", "type": "string", "const": "allow-check-full-disk-access-permission", "markdownDescription": "Enables the check_full_disk_access_permission command without any pre-configured scope."}, {"description": "Denies the check_full_disk_access_permission command without any pre-configured scope.", "type": "string", "const": "deny-check-full-disk-access-permission", "markdownDescription": "Denies the check_full_disk_access_permission command without any pre-configured scope."}, {"description": "Enables the check_input_monitoring_permission command without any pre-configured scope.", "type": "string", "const": "allow-check-input-monitoring-permission", "markdownDescription": "Enables the check_input_monitoring_permission command without any pre-configured scope."}, {"description": "Denies the check_input_monitoring_permission command without any pre-configured scope.", "type": "string", "const": "deny-check-input-monitoring-permission", "markdownDescription": "Denies the check_input_monitoring_permission command without any pre-configured scope."}, {"description": "Enables the check_microphone_permission command without any pre-configured scope.", "type": "string", "const": "allow-check-microphone-permission", "markdownDescription": "Enables the check_microphone_permission command without any pre-configured scope."}, {"description": "Denies the check_microphone_permission command without any pre-configured scope.", "type": "string", "const": "deny-check-microphone-permission", "markdownDescription": "Denies the check_microphone_permission command without any pre-configured scope."}, {"description": "Enables the check_photokit_permission command without any pre-configured scope.", "type": "string", "const": "allow-check-photokit-permission", "markdownDescription": "Enables the check_photokit_permission command without any pre-configured scope."}, {"description": "Denies the check_photokit_permission command without any pre-configured scope.", "type": "string", "const": "deny-check-photokit-permission", "markdownDescription": "Denies the check_photokit_permission command without any pre-configured scope."}, {"description": "Enables the check_screen_recording_permission command without any pre-configured scope.", "type": "string", "const": "allow-check-screen-recording-permission", "markdownDescription": "Enables the check_screen_recording_permission command without any pre-configured scope."}, {"description": "Denies the check_screen_recording_permission command without any pre-configured scope.", "type": "string", "const": "deny-check-screen-recording-permission", "markdownDescription": "Denies the check_screen_recording_permission command without any pre-configured scope."}, {"description": "Enables the get_photokit_permission_listeners command without any pre-configured scope.", "type": "string", "const": "allow-get-photokit-permission-listeners", "markdownDescription": "Enables the get_photokit_permission_listeners command without any pre-configured scope."}, {"description": "Denies the get_photokit_permission_listeners command without any pre-configured scope.", "type": "string", "const": "deny-get-photokit-permission-listeners", "markdownDescription": "Denies the get_photokit_permission_listeners command without any pre-configured scope."}, {"description": "Enables the register_photokit_permission_listener command without any pre-configured scope.", "type": "string", "const": "allow-register-photokit-permission-listener", "markdownDescription": "Enables the register_photokit_permission_listener command without any pre-configured scope."}, {"description": "Denies the register_photokit_permission_listener command without any pre-configured scope.", "type": "string", "const": "deny-register-photokit-permission-listener", "markdownDescription": "Denies the register_photokit_permission_listener command without any pre-configured scope."}, {"description": "Enables the request_accessibility_permission command without any pre-configured scope.", "type": "string", "const": "allow-request-accessibility-permission", "markdownDescription": "Enables the request_accessibility_permission command without any pre-configured scope."}, {"description": "Denies the request_accessibility_permission command without any pre-configured scope.", "type": "string", "const": "deny-request-accessibility-permission", "markdownDescription": "Denies the request_accessibility_permission command without any pre-configured scope."}, {"description": "Enables the request_camera_permission command without any pre-configured scope.", "type": "string", "const": "allow-request-camera-permission", "markdownDescription": "Enables the request_camera_permission command without any pre-configured scope."}, {"description": "Denies the request_camera_permission command without any pre-configured scope.", "type": "string", "const": "deny-request-camera-permission", "markdownDescription": "Denies the request_camera_permission command without any pre-configured scope."}, {"description": "Enables the request_full_disk_access_permission command without any pre-configured scope.", "type": "string", "const": "allow-request-full-disk-access-permission", "markdownDescription": "Enables the request_full_disk_access_permission command without any pre-configured scope."}, {"description": "Denies the request_full_disk_access_permission command without any pre-configured scope.", "type": "string", "const": "deny-request-full-disk-access-permission", "markdownDescription": "Denies the request_full_disk_access_permission command without any pre-configured scope."}, {"description": "Enables the request_input_monitoring_permission command without any pre-configured scope.", "type": "string", "const": "allow-request-input-monitoring-permission", "markdownDescription": "Enables the request_input_monitoring_permission command without any pre-configured scope."}, {"description": "Denies the request_input_monitoring_permission command without any pre-configured scope.", "type": "string", "const": "deny-request-input-monitoring-permission", "markdownDescription": "Denies the request_input_monitoring_permission command without any pre-configured scope."}, {"description": "Enables the request_microphone_permission command without any pre-configured scope.", "type": "string", "const": "allow-request-microphone-permission", "markdownDescription": "Enables the request_microphone_permission command without any pre-configured scope."}, {"description": "Denies the request_microphone_permission command without any pre-configured scope.", "type": "string", "const": "deny-request-microphone-permission", "markdownDescription": "Denies the request_microphone_permission command without any pre-configured scope."}, {"description": "Enables the request_photokit_permission command without any pre-configured scope.", "type": "string", "const": "allow-request-photokit-permission", "markdownDescription": "Enables the request_photokit_permission command without any pre-configured scope."}, {"description": "Denies the request_photokit_permission command without any pre-configured scope.", "type": "string", "const": "deny-request-photokit-permission", "markdownDescription": "Denies the request_photokit_permission command without any pre-configured scope."}, {"description": "Enables the request_screen_recording_permission command without any pre-configured scope.", "type": "string", "const": "allow-request-screen-recording-permission", "markdownDescription": "Enables the request_screen_recording_permission command without any pre-configured scope."}, {"description": "Denies the request_screen_recording_permission command without any pre-configured scope.", "type": "string", "const": "deny-request-screen-recording-permission", "markdownDescription": "Denies the request_screen_recording_permission command without any pre-configured scope."}, {"description": "Enables the unregister_photokit_permission_listener command without any pre-configured scope.", "type": "string", "const": "allow-unregister-photokit-permission-listener", "markdownDescription": "Enables the unregister_photokit_permission_listener command without any pre-configured scope."}, {"description": "Denies the unregister_photokit_permission_listener command without any pre-configured scope.", "type": "string", "const": "deny-unregister-photokit-permission-listener", "markdownDescription": "Denies the unregister_photokit_permission_listener command without any pre-configured scope."}, {"description": "Default permissions for the plugin\n#### This default permission set includes:\n\n- `allow-check-accessibility-permission`\n- `allow-request-accessibility-permission`\n- `allow-check-full-disk-access-permission`\n- `allow-request-full-disk-access-permission`\n- `allow-check-screen-recording-permission`\n- `allow-request-screen-recording-permission`\n- `allow-check-microphone-permission`\n- `allow-request-microphone-permission`\n- `allow-check-camera-permission`\n- `allow-request-camera-permission`\n- `allow-check-input-monitoring-permission`\n- `allow-request-input-monitoring-permission`\n- `allow-check-photokit-permission`\n- `allow-request-photokit-permission`\n- `allow-register-photokit-permission-listener`\n- `allow-unregister-photokit-permission-listener`\n- `allow-get-photokit-permission-listeners`", "type": "string", "const": "default", "markdownDescription": "Default permissions for the plugin\n#### This default permission set includes:\n\n- `allow-check-accessibility-permission`\n- `allow-request-accessibility-permission`\n- `allow-check-full-disk-access-permission`\n- `allow-request-full-disk-access-permission`\n- `allow-check-screen-recording-permission`\n- `allow-request-screen-recording-permission`\n- `allow-check-microphone-permission`\n- `allow-request-microphone-permission`\n- `allow-check-camera-permission`\n- `allow-request-camera-permission`\n- `allow-check-input-monitoring-permission`\n- `allow-request-input-monitoring-permission`\n- `allow-check-photokit-permission`\n- `allow-request-photokit-permission`\n- `allow-register-photokit-permission-listener`\n- `allow-unregister-photokit-permission-listener`\n- `allow-get-photokit-permission-listeners`"}]}}}