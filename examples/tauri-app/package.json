{"name": "tauri-app", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@tauri-apps/api": "^2", "ahooks": "^3.8.1", "antd": "^5.22.1", "react": "^18.2.0", "react-dom": "^18.2.0", "tauri-plugin-macos-permissions-api": "link:../../"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.2.1", "release-it": "^17.10.0", "typescript": "^5.2.2", "vite": "^5.3.1"}}